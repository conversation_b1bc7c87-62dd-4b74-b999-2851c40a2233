'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
interface CaseStudy {
  id: bigint
  twitter_account: string
  twitter_link: string
  description: string
  categories: string[]
  date_of_post: Date
  impressions: bigint
  likes: bigint
  retweets: bigint
}

interface CaseStudyFormData {
  twitter_link: string;
  twitter_account: string;
  description: string;
  categories: string[];
  impressions: number;
  likes: number;
  retweets: number;
}
import { useState } from "react"
import { createBrowserClient } from '@supabase/ssr'
import { toast } from "sonner"
import { logger } from "@/lib/logger"

interface SupabaseError {
  message: string
}

interface FormData {
  twitter_link: string
  twitter_account: string
  description: string
  categories: string[]
  impressions: number
  likes: number
  retweets: number
}

interface Props {
  caseStudy?: CaseStudy
  onSubmit: (data: CaseStudyFormData) => Promise<void>
  trigger?: React.ReactNode
}

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

interface CaseStudyDialogProps {
  caseStudy: CaseStudy | null
  isOpen: boolean
  onClose: () => void
  onCaseStudyChange: (caseStudy: CaseStudy | null) => void
}

export function CaseStudyDialog({
  caseStudy,
  isOpen,
  onClose,
  onCaseStudyChange
}: CaseStudyDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    twitter_link: caseStudy?.twitter_link || '',
    twitter_account: caseStudy?.twitter_account || '',
    description: caseStudy?.description || '',
    categories: caseStudy?.categories || [],
    impressions: caseStudy ? Number(caseStudy.impressions.toString()) : 0,
    likes: caseStudy ? Number(caseStudy.likes.toString()) : 0,
    retweets: caseStudy ? Number(caseStudy.retweets.toString()) : 0,
  })

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    // Validation
    const requiredFields = ['twitter_account', 'twitter_link', 'description'] as const
    const missingFields = requiredFields.filter(field => !formData[field])

    if (missingFields.length > 0) {
      const message = `Please fill in: ${missingFields.join(', ')}`
      toast.error(message)
      logger.warn('ui', 'Form validation failed', { missingFields })
      return
    }

    // Validate Twitter URL format
    if (!formData.twitter_link.includes('twitter.com') && !formData.twitter_link.includes('x.com')) {
      toast.error('Please enter a valid Twitter/X URL')
      logger.warn('ui', 'Invalid Twitter URL format', { url: formData.twitter_link })
      return
    }

    const endTimer = logger.startTimer('Case study submission')

    try {
      setLoading(true)
      logger.info('ui', 'Submitting case study', {
        twitter_account: formData.twitter_account,
        has_description: !!formData.description
      })

      const categoriesArray = Array.isArray(formData.categories)
        ? formData.categories
        : (formData.categories as string).split(',').map((category: string) => category.trim()).filter(Boolean)

      const submissionData = {
        twitter_link: formData.twitter_link.trim(),
        twitter_account: formData.twitter_account.trim(),
        description: formData.description.trim(),
        categories: categoriesArray,
        impressions: Math.max(0, formData.impressions),
        likes: Math.max(0, formData.likes),
        retweets: Math.max(0, formData.retweets),
      }

      // Save to Supabase
      let savedCaseStudy: CaseStudy

      if (caseStudy) {
        // Update existing case study
        const { data, error } = await supabase
          .from('case_studies')
          .update({
            twitter_link: submissionData.twitter_link,
            twitter_account: submissionData.twitter_account,
            description: submissionData.description,
            categories: submissionData.categories,
            impressions: submissionData.impressions,
            likes: submissionData.likes,
            retweets: submissionData.retweets,
          })
          .eq('id', caseStudy.id)
          .select()
          .single()

        if (error) throw error

        savedCaseStudy = {
          ...data,
          id: BigInt(data.id),
          impressions: BigInt(data.impressions),
          likes: BigInt(data.likes),
          retweets: BigInt(data.retweets),
          date_of_post: new Date(data.date_of_post),
        }
      } else {
        // Create new case study
        const { data, error } = await supabase
          .from('case_studies')
          .insert({
            twitter_link: submissionData.twitter_link,
            twitter_account: submissionData.twitter_account,
            description: submissionData.description,
            categories: submissionData.categories,
            impressions: submissionData.impressions,
            likes: submissionData.likes,
            retweets: submissionData.retweets,
            date_of_post: new Date().toISOString(),
          })
          .select()
          .single()

        if (error) throw error

        savedCaseStudy = {
          ...data,
          id: BigInt(data.id),
          impressions: BigInt(data.impressions),
          likes: BigInt(data.likes),
          retweets: BigInt(data.retweets),
          date_of_post: new Date(data.date_of_post),
        }
      }

      logger.info('ui', 'Case study submitted successfully')
      toast.success('Case study saved successfully!')

      // Notify parent component
      onCaseStudyChange(savedCaseStudy)
      onClose()
      // Reset form
      setFormData({
        twitter_link: '',
        twitter_account: '',
        description: '',
        categories: [],
        impressions: 0,
        likes: 0,
        retweets: 0,
      })
    } catch (error) {
      const supabaseError = error as SupabaseError
      const errorMessage = supabaseError.message || 'Something went wrong'

      logger.error('ui', 'Case study submission failed', error as Error, {
        formData: { ...formData, description: '[REDACTED]' }
      })

      toast.error(errorMessage)
    } finally {
      setLoading(false)
      endTimer()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === 'impressions' || name === 'likes' || name === 'retweets'
        ? parseInt(value) || 0
        : value
    }))
  }

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{caseStudy ? "Edit Case Study" : "Add Case Study"}</DialogTitle>
            <DialogDescription>
              Make changes to the case study here. Click save when you&apos;re done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="twitter_account" className="text-right">
                Account
              </Label>
              <Input
                id="twitter_account"
                name="twitter_account"
                value={formData.twitter_account}
                onChange={handleInputChange}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="twitter_link" className="text-right">
                Tweet URL
              </Label>
              <Input
                id="twitter_link"
                name="twitter_link"
                value={formData.twitter_link}
                onChange={handleInputChange}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="impressions" className="text-right">
                Impressions
              </Label>
              <Input
                id="impressions"
                name="impressions"
                type="number"
                value={formData.impressions}
                onChange={handleInputChange}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="likes" className="text-right">
                Likes
              </Label>
              <Input
                id="likes"
                name="likes"
                type="number"
                value={formData.likes}
                onChange={handleInputChange}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="retweets" className="text-right">
                Retweets
              </Label>
              <Input
                id="retweets"
                name="retweets"
                type="number"
                value={formData.retweets}
                onChange={handleInputChange}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="categories" className="text-right">
                Categories
              </Label>
              <Input
                id="categories"
                name="categories"
                value={formData.categories?.join(', ')}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="Comma-separated categories"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleTextareaChange}
                className="col-span-3"
                required
              />
            </div>
          </div>
          <DialogFooter className="gap-2">
            <Button type="submit" disabled={loading}>
              {loading ? "Saving..." : "Save changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
