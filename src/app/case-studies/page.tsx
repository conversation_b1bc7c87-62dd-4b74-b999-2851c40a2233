import { CaseStudiesClient } from '../client'
import { createClient } from '@/lib/supabase-server'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

async function getCaseStudies() {
  try {
    // Get Supabase client
    const supabase = await createClient()

    // Fetch case studies with all data
    const { data: caseStudies, error } = await supabase
      .from('case_studies')
      .select('*')
      .order('date_of_post', { ascending: false })

    if (error) {
      console.error('Error fetching case studies:', error)
      return {
        caseStudies: [],
        categories: []
      }
    }

    // Get unique categories from all case studies
    const uniqueCategories = Array.from(
      new Set(
        caseStudies?.flatMap(study => study.categories) || []
      )
    ).sort()

    // Transform the data to match our CaseStudy type
    const transformedStudies = caseStudies?.map(study => ({
      id: BigInt(study.id),
      twitter_account: study.twitter_account,
      twitter_link: study.twitter_link,
      impressions: BigInt(study.impressions),
      likes: BigInt(study.likes),
      retweets: BigInt(study.retweets),
      description: study.description,
      categories: study.categories,
      date_of_post: new Date(study.date_of_post),
    })) || []

    return {
      caseStudies: transformedStudies,
      categories: uniqueCategories,
    }
  } catch (error) {
    console.error('Error in getCaseStudies:', error)
    return {
      caseStudies: [],
      categories: []
    }
  }
}

// This is the main server component that fetches case studies data
export default async function MediaCaseStudiesPage() {
  const { caseStudies, categories } = await getCaseStudies()

  const department = {
    id: 'media',
    name: 'Media Team',
    description: 'Media department case studies',
    case_studies_admin_level: 'admin' as const,
  }

  return (
    <div className="h-full">
      <CaseStudiesClient
        department={department}
        allCaseStudies={caseStudies}
        allLabels={categories}
      />
    </div>
  )
}