'use client'

import { CaseStudiesClient } from '../../client'
import { createBrowserClient } from '@supabase/ssr'
import { env } from '@/lib/env'
import { NavBar } from "@/components/ui/tubelight-navbar"
import { Home, Globe, FileText } from "lucide-react"
import { useEffect, useState } from 'react'
import { motion } from "framer-motion"

interface CaseStudy {
  id: bigint
  twitter_account: string
  twitter_link: string
  impressions: bigint
  likes: bigint
  retweets: bigint
  description: string
  categories: string[]
  date_of_post: Date
}

export default function MediaCaseStudiesPage() {
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchCaseStudies() {
      try {
        setIsLoading(true)

        // Create Supabase client
        const supabase = createBrowserClient(
          env.NEXT_PUBLIC_SUPABASE_URL,
          env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        )

        const { data, error } = await supabase
          .from('case_studies')
          .select('*')
          .order('date_of_post', { ascending: false })

        if (error) {
          console.error('Error fetching case studies:', error)
          return
        }

        // Get unique categories
        const uniqueCategories = Array.from(
          new Set(
            data?.flatMap(study => study.categories) || []
          )
        ).sort()

        // Transform the data
        const transformedStudies = data?.map(study => ({
          id: BigInt(study.id),
          twitter_account: study.twitter_account,
          twitter_link: study.twitter_link,
          impressions: BigInt(study.impressions),
          likes: BigInt(study.likes),
          retweets: BigInt(study.retweets),
          description: study.description,
          categories: study.categories,
          date_of_post: new Date(study.date_of_post),
        })) || []

        setCaseStudies(transformedStudies)
        setCategories(uniqueCategories)
      } catch (error) {
        console.error('Error in fetchCaseStudies:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchCaseStudies()
  }, [])

  const department = {
    id: 'media',
    name: 'Media Team',
    description: 'Media department case studies',
    case_studies_admin_level: 'admin' as const,
  }

  const navItems = [
    {
      name: "Home",
      url: "/",
      icon: Home
    },
    {
      name: "Media",
      url: "/case-studies/social-media",
      icon: Globe
    },
    {
      name: "Marketing",
      url: "/case-studies/marketing",
      icon: FileText
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <NavBar items={navItems} />
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="pt-24 container mx-auto px-4"
      >
        {isLoading ? (
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <CaseStudiesClient
            department={department}
            allCaseStudies={caseStudies}
            allLabels={categories}
          />
        )}
      </motion.div>
    </div>
  )
}